# ADR-0007: Change from Watch-Only to Independent Companion App Architecture

*   **Status:** Accepted
*   **Date:** 2024-12-13
*   **Deciders:** Dr. Muscle Development Team
*   **Consulted:** Apple Developer Documentation, iOS/watchOS Development Community
*   **Informed:** All project stakeholders

## Context and Problem Statement

The Dr. Muscle Apple Watch app was initially conceived as a watch-only app (standalone watchOS app with no iOS component). During the development and App Store submission process, we encountered significant technical challenges that necessitated a change in architecture to an independent companion app model.

The key issue was that Apple's App Store submission process and distribution model have specific requirements that made the watch-only approach problematic for our use case.

## Decision Drivers

*   **App Store Submission Requirements:** Apple requires specific bundle configurations and submission processes that are more complex for watch-only apps
*   **User Discovery:** Watch-only apps have limited visibility in the App Store, making it harder for users to find and install
*   **Future Flexibility:** Need to potentially add iOS-specific features or deeper integration in the future
*   **Universal Purchase Support:** Better support for shared in-app purchases and subscriptions across platforms
*   **Build Process Complexity:** Watch-only apps require a different build and archive process that was causing submission errors
*   **TestFlight Distribution:** Encountered "Unable to determine app platform for 'Undefined' software type" errors with watch-only configuration

## Considered Options

*   **Option 1: Watch-Only App**
    *   *Pros:* 
        - Simpler conceptually (single platform)
        - No need to maintain an iOS app
        - Smaller download size
    *   *Cons:* 
        - Complex App Store submission process
        - Limited discoverability in App Store
        - Cannot leverage iOS features if needed
        - Build configuration errors during TestFlight upload
        - Requires `INFOPLIST_KEY_WKWatchOnly = YES` which caused archive issues

*   **Option 2: Dependent Companion App**
    *   *Pros:* 
        - Traditional watchOS app model
        - Well-documented process
    *   *Cons:* 
        - Requires iOS app to be installed first
        - Poor user experience for watch-first users
        - Goes against our goal of standalone watch functionality

*   **Option 3: Independent Companion App**
    *   *Pros:* 
        - Watch app can run without iOS app installed
        - Single App Store listing with better visibility
        - Flexible installation (users choose which platforms)
        - Standard build and submission process
        - Better support for universal purchases
        - Future-proof for iOS features
    *   *Cons:* 
        - Requires minimal iOS app (though it can be a simple landing page)
        - Slightly larger download size due to iOS component

## Decision Outcome

**Chosen Option:** Independent Companion App, because it provides the best balance of user experience, technical feasibility, and future flexibility.

This architecture allows us to:
1. Maintain the core goal of a standalone watch experience (users can install just the watch app)
2. Use Apple's standard build and submission process (avoiding the complex watch-only workflow)
3. Provide better App Store visibility and discoverability
4. Enable future iOS features if needed without architectural changes
5. Support universal purchases and subscriptions across both platforms

The key configuration changes:
- Set `INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES` (enables independence)
- Remove `INFOPLIST_KEY_WKWatchOnly` (this was causing submission issues)
- Set `SKIP_INSTALL = YES` for watchOS target (embeds it in iOS app)
- Set `SKIP_INSTALL = NO` for iOS target (makes it the top-level product)
- Archive using the iOS scheme, not the watch scheme
- Submit to TestFlight as an iOS app (`-t ios`)

### Negative Consequences

*   Must maintain a minimal iOS app (even if it's just a landing page)
*   Slightly more complex project structure with two targets
*   Developers must understand the distinction between watch-only and independent companion apps
*   Build process must always use iOS scheme for archiving (common source of confusion)

## Implementation Plan

1. Remove `INFOPLIST_KEY_WKWatchOnly` from all build configurations
2. Add `INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES` to watch target
3. Create minimal iOS app with landing page directing users to Apple Watch
4. Configure `SKIP_INSTALL` settings correctly for both targets
5. Update CI/CD pipeline to archive iOS scheme instead of watch scheme
6. Update all documentation to reflect this architectural decision
7. Test installation scenarios (iOS only, watch only, both)

## Validation

Success criteria:
*   TestFlight upload succeeds without platform errors
*   App appears in App Store Connect with both iOS and watchOS versions
*   Users can install watch app without iOS app
*   Users can install iOS app without watch app
*   In-app purchases work across both platforms

## Links

*   [Apple: Creating Independent watchOS Apps](https://developer.apple.com/documentation/watchkit/creating-independent-watchos-apps)
*   [Project Status Document](../status.md) - Contains debugging history
*   [Build Workflow](.github/workflows/apple-watch-build-workflow.yml)