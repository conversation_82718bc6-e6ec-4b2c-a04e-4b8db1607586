# Dr. Muscle Apple Watch App - Automated Tests

## Purpose

This document lists the automated tests implemented for the project and their current status. Tests should follow BDD-style (Given/When/Then) using XCTest.

## Instructions

*   Add tests here as they are created during the RED stage of the TDD cycle.
*   Update status from "Failing" to "Passing" during the DOCUMENTATION stage.
*   Group tests by feature or component.

## Test Suites

### Project Setup

*   `testAppInitialization` - Status: Passing
    * Tests that the app initializes correctly with SwiftUI App lifecycle
*   `testContentViewInitialization` - Status: Passing
    * Tests that the ContentView initializes correctly and displays the basic UI

### Add Set / Next Exercise Choice Screen

*   `testChoiceScreenAppearsAfterLastPlannedSet` - Status: Passing
    * Tests that the choice screen appears after saving the last planned set
*   `testChoiceScreenDoesNotAppearAfterNonLastSet` - Status: Passing
    * Tests that the choice screen does not appear after saving a non-last set
*   `testAddSetAction` - Status: Passing
    * Tests that tapping "Add Set" reverts to the Set Screen with the same values
*   `testAddSetPreservesRepsAndWeight` - Status: Passing
    * Tests that tapping "Add Set" preserves the reps and weight from the completed set
*   `testNextExerciseAction` - Status: Passing
    * Tests that tapping "Next Exercise" navigates to the next exercise
*   `testTimerExpiryBehavior` - Status: Passing
    * Tests that the timer expiry behavior works correctly

### Exercise Transition

*   `testNextExerciseActionShowsCheckmarkAnimation` - Status: Passing
    * Tests that the "Next Exercise" action shows the checkmark animation
*   `testCheckmarkAnimationCompletesAndNavigatesToNextExercise` - Status: Passing
    * Tests that when the checkmark animation completes, it navigates to the next exercise
*   `testSkippingInterExerciseRest` - Status: Passing
    * Tests that tapping "Next Exercise" during the rest timer skips the timer and shows the checkmark animation
*   `testInterExerciseRestTimerCompletes` - Status: Passing
    * Tests that when the inter-exercise rest timer completes, the choice screen remains visible
*   `testProceedingAfterTimerExpiry` - Status: Passing
    * Tests that tapping "Next Exercise" after the timer expires navigates to the next exercise
*   `testNavigationToWorkoutCompleteScreen` - Status: Passing
    * Tests that when the last exercise is completed, it navigates to the workout complete screen

### Workout Complete

*   `testWorkoutCompleteViewDisplaysCorrectContent` - Status: Passing
    * Tests that the workout complete view displays the correct content
*   `testFinishButtonCallsViewModel` - Status: Passing
    * Tests that tapping the finish button calls the view model's finishWorkout method
*   `testFinishWorkout` - Status: Passing
    * Tests that the finishWorkout method marks the workout as completed locally and syncs with the server
*   `testFinishWorkoutHandlesStorageError` - Status: Passing
    * Tests that the finishWorkout method handles storage errors gracefully
*   `testFinishWorkoutHandlesAPIError` - Status: Passing
    * Tests that the finishWorkout method handles API errors gracefully

### Authentication

#### AuthenticationManager Tests (AuthenticationManagerTests.swift)
*   `testAuthenticationManagerInitialState` - Status: Failing
    * Tests that the authentication manager initializes with no authenticated user
*   `testHandleSuccessfulSignInWithApple` - Status: Failing
    * Tests that the authentication manager can handle a successful Sign in with Apple
*   `testHandleSignInWithAppleError` - Status: Failing
    * Tests that the authentication manager handles Sign in with Apple errors correctly
*   `testStoreAndRetrieveAuthState` - Status: Failing
    * Tests that the authentication manager can store and retrieve authentication state securely
*   `testClearAuthState` - Status: Failing
    * Tests that the authentication manager can clear stored authentication state
*   `testAuthorizationControllerDidCompleteWithAuthorization` - Status: Failing
    * Tests the ASAuthorizationControllerDelegate method for successful authorization
*   `testAuthorizationControllerDidCompleteWithError` - Status: Failing
    * Tests the ASAuthorizationControllerDelegate method for authorization errors

#### LoginView Tests (LoginViewTests.swift)
*   `testLoginViewDisplaysSignInWithAppleButton` - Status: Failing
    * Tests that the login view displays the Sign in with Apple button
*   `testSignInWithAppleButtonHasCorrectStyle` - Status: Failing
    * Tests that the Sign in with Apple button follows watchOS styling guidelines
*   `testTappingSignInWithAppleButtonInitiatesSignIn` - Status: Failing
    * Tests that tapping the Sign in with Apple button initiates the sign-in process
*   `testLoginViewDoesNotShowQuickSignInButton` - Status: Failing
    * Tests that the mock "Quick Sign In" button has been removed
*   `testLoginViewShowsAppBranding` - Status: Failing
    * Tests that the login view displays app logo and name
*   `testLoginViewShowsErrorMessage` - Status: Failing
    * Tests that the login view displays error messages when authentication fails
*   `testLoginViewShowsLoadingIndicatorDuringAuth` - Status: Failing
    * Tests that the login view shows loading state during authentication
*   `testSignInButtonDisabledDuringAuthentication` - Status: Failing
    * Tests that the Sign in button is disabled while authenticating

#### LoginViewModel Tests (LoginViewModelTests.swift)
*   `testLoginViewModelInitialState` - Status: Failing
    * Tests that the login view model initializes with correct default state
*   `testStartSignInWithApple` - Status: Failing
    * Tests that starting Sign in with Apple sets the correct state
*   `testHandleSuccessfulSignIn` - Status: Failing
    * Tests handling of successful Sign in with Apple credential
*   `testHandleSignInCancellation` - Status: Failing
    * Tests that user cancellation is handled gracefully without error
*   `testHandleSignInError` - Status: Failing
    * Tests that Sign in with Apple errors are displayed appropriately
*   `testHandleNetworkError` - Status: Failing
    * Tests that network errors show appropriate messages
*   `testHandleBackendValidationSuccess` - Status: Failing
    * Tests successful backend validation of Apple credentials
*   `testHandleBackendValidationError` - Status: Failing
    * Tests handling of backend validation errors
*   `testCreateAuthorizationRequest` - Status: Failing
    * Tests that authorization requests are properly configured
*   `testShouldRemoveQuickSignInMethod` - Status: Failing
    * Tests that the mock quickSignIn method has been removed

### API Client

*   `testAPIClientInitialization` - Status: Passing
    * Tests that the API client initializes correctly with the correct base URL
*   `testTokenStorageAndRetrieval` - Status: Passing
    * Tests that the API client can store and clear authentication tokens

### Workout List

*   `testFetchWorkoutsSuccess` - Status: Passing
    * Tests that the WorkoutListViewModel can fetch workouts from the API successfully
*   `testFetchWorkoutsEmpty` - Status: Passing
    * Tests that the WorkoutListViewModel handles empty workout lists correctly
*   `testFetchWorkoutsError` - Status: Passing
    * Tests that the WorkoutListViewModel handles API errors correctly
*   `testInitialState` - Status: Passing
    * Tests that the WorkoutListViewModel initializes with the correct state
*   `testWorkoutListViewDisplaysWorkouts` - Status: Passing
    * Tests that the WorkoutListView displays workouts correctly
*   `testWorkoutListViewDisplaysEmptyState` - Status: Passing
    * Tests that the WorkoutListView displays the empty state correctly
*   `testWorkoutListViewDisplaysErrorState` - Status: Passing
    * Tests that the WorkoutListView displays the error state correctly
*   `testWorkoutListViewDisplaysLoadingState` - Status: Passing
    * Tests that the WorkoutListView displays the loading state correctly

### Core Data Storage

*   `testCoreDataModelInitialization` - Status: Passing
    * Tests that the Core Data model initializes correctly with the proper entities
*   `testSavingWorkout` - Status: Passing
    * Tests saving a workout to Core Data and retrieving it
*   `testSavingExercise` - Status: Passing
    * Tests saving an exercise to Core Data and retrieving it
*   `testSavingSetLog` - Status: Passing
    * Tests saving a set log to Core Data and retrieving it
*   `testFetchingWithPredicates` - Status: Passing
    * Tests fetching data with various predicates for filtering
*   `testPersistenceControllerInitialization` - Status: Passing
    * Tests that the PersistenceController initializes correctly with the proper configuration

### Storage Service

*   `testSaveAndRetrieveWorkout` - Status: Passing
    * Tests saving and retrieving a workout using the StorageService
*   `testSaveAndRetrieveExercise` - Status: Passing
    * Tests saving and retrieving an exercise using the StorageService
*   `testSaveAndRetrieveSetLog` - Status: Passing
    * Tests saving and retrieving a set log using the StorageService
*   `testFetchSetLogsNeedingSync` - Status: Passing
    * Tests fetching set logs that need to be synced with the server
*   `testMarkSetLogAsSynced` - Status: Passing
    * Tests marking a set log as synced
*   `testDeleteWorkout` - Status: Passing
    * Tests deleting a workout and its associated exercises and set logs

### Workout Detail

*   `testFetchWorkoutDetailsSuccess` - Status: Passing
    * Tests that the WorkoutDetailViewModel can fetch workout details from the API successfully
*   `testFetchWorkoutDetailsError` - Status: Passing
    * Tests that the WorkoutDetailViewModel handles API errors correctly
*   `testStartWorkoutSuccess` - Status: Passing
    * Tests that the WorkoutDetailViewModel can start a workout successfully
*   `testStartWorkoutWithoutWorkout` - Status: Passing
    * Tests that the WorkoutDetailViewModel handles starting a workout without loaded workout details
*   `testWorkoutDetailViewDisplaysWorkoutDetails` - Status: Passing
    * Tests that the WorkoutDetailView displays workout details correctly
*   `testWorkoutDetailViewDisplaysErrorState` - Status: Passing
    * Tests that the WorkoutDetailView displays the error state correctly
*   `testWorkoutDetailViewDisplaysLoadingState` - Status: Passing
    * Tests that the WorkoutDetailView displays the loading state correctly

### Set Screen

*   `testFetchExerciseDetailsSuccess` - Status: Passing
    * Tests that the SetViewModel can fetch exercise details from the API successfully
*   `testFetchExerciseDetailsError` - Status: Passing
    * Tests that the SetViewModel handles API errors correctly
*   `testMoveToNextSet` - Status: Passing
    * Tests that the SetViewModel can move to the next set correctly
*   `testMoveToNextSetAtEnd` - Status: Passing
    * Tests that the SetViewModel handles moving to the next set when at the end of the sets
*   `testUpdateReps` - Status: Passing
    * Tests that the SetViewModel can update the current reps correctly
*   `testUpdateWeight` - Status: Passing
    * Tests that the SetViewModel can update the current weight correctly
*   `testComputedProperties` - Status: Passing
    * Tests that the SetViewModel's computed properties work correctly
*   `testSaveCurrentSet` - Status: Passing
    * Tests that the SetViewModel can save the current set data to local storage
*   `testSaveCurrentSetWithRIR` - Status: Passing
    * Tests that the SetViewModel can save the current set data with RIR to local storage
*   `testSetViewDisplaysExerciseDetails` - Status: Passing
    * Tests that the SetView displays exercise details correctly
*   `testSetViewDisplaysErrorState` - Status: Passing
    * Tests that the SetView displays the error state correctly
*   `testSetViewDisplaysLoadingState` - Status: Passing
    * Tests that the SetView displays the loading state correctly
*   `testWeightPickerUpdatesViewModel` - Status: Passing
    * Tests that the weight picker updates the view model correctly when a new weight is selected

### RIR Picker

*   `testRIRPickerInitialState` - Status: Passing
    * Tests that the RIRPicker component initializes correctly with the provided initial RIR value
*   `testRIRPickerOptions` - Status: Passing
    * Tests that the RIRPicker component has the correct descriptive options with the correct values
*   `testRIRPickerSelection` - Status: Passing
    * Tests that the RIRPicker component correctly handles selection of an option
*   `testRIRPickerDisplayedForFirstWorkSet` - Status: Passing
    * Tests that the RIR picker is displayed only for the first work set
*   `testRIRPickerNotDisplayedForWarmupSet` - Status: Passing
    * Tests that the RIR picker is not displayed for warmup sets
*   `testRIRPickerNotDisplayedForSubsequentWorkSet` - Status: Passing
    * Tests that the RIR picker is not displayed for subsequent work sets
*   `testSaveSetWithRIRValue` - Status: Passing
    * Tests that the RIR value is saved correctly with the set
*   `testHandleMissingAPIFlagGracefully` - Status: Passing
    * Tests that missing API flags are handled gracefully
*   `testShouldShowRIRPickerLogic` - Status: Passing
    * Tests the shouldShowRIRPicker method with various combinations of isFirstWorkSet and isWarmup

### Timer Functionality

*   `testTimerButtonInitialState` - Status: Passing
    * Tests that the TimerButton component initializes correctly with the provided initial values
*   `testTimerButtonTimerActiveState` - Status: Passing
    * Tests that the TimerButton component correctly displays the timer when active
*   `testTimerButtonFormatting` - Status: Passing
    * Tests that the TimerButton component correctly formats different time values
*   `testSetViewModelTimerInitialization` - Status: Passing
    * Tests that the SetViewModel correctly initializes timer values from a set recommendation
*   `testSetViewTimerActivation` - Status: Passing
    * Tests that the SetViewModel correctly activates the timer
*   `testTimerCountdown` - Status: Passing
    * Tests that the timer correctly counts down each second
*   `testTimerCompletion` - Status: Passing
    * Tests that the timer correctly completes when it reaches zero
*   `testSkipTimer` - Status: Passing
    * Tests that the timer can be skipped by the user

### Performance Calculation

*   `testPerformanceCalculationWithNoHistoricalData` - Status: Passing
    * Tests that performance percentage is calculated correctly when no historical data is available
*   `testPerformanceCalculationWithHistoricalData` - Status: Passing
    * Tests that performance percentage is calculated correctly when historical data is available
*   `testPositivePerformanceChange` - Status: Passing
    * Tests that positive performance changes are calculated correctly
*   `testNegativePerformanceChange` - Status: Passing
    * Tests that negative performance changes are calculated correctly
*   `testNoPerformanceChange` - Status: Passing
    * Tests that zero performance change is calculated correctly
*   `testPerformanceRecalculationAfterRepsChange` - Status: Passing
    * Tests that performance percentage is recalculated correctly when reps change
*   `testPerformanceRecalculationAfterWeightChange` - Status: Passing
    * Tests that performance percentage is recalculated correctly when weight changes
*   `testOneRMCalculationUsingEpleyFormula` - Status: Passing
    * Tests that one-rep max is calculated correctly using the Epley formula

### Offline Functionality

*   `testStartWorkoutRequiresConnection` - Status: Passing
    * Tests that starting a workout requires network connectivity
*   `testWorkoutInProgressContinuesOffline` - Status: Passing
    * Tests that a workout in progress can continue if connection is lost
*   `testLoseConnectionDuringRestTimer` - Status: Passing
    * Tests that the app can handle losing connection during rest timer
*   `testLoseConnectionDuringChoiceScreen` - Status: Passing
    * Tests that the app can handle losing connection during the "Add Set" / "Next Exercise" choice screen
*   `testCompleteWorkoutOffline` - Status: Passing
    * Tests that a workout can be completed offline

### HealthKit Integration

*   `testRequestAuthorizationOnFirstWorkoutStart` - Status: Passing
    * Tests that the HealthKitService requests authorization on first workout start
*   `testAuthorizationGranted` - Status: Passing
    * Tests that the HealthKitService handles authorization granted
*   `testAuthorizationDenied` - Status: Passing
    * Tests that the HealthKitService handles authorization denied
*   `testAuthorizationAlreadyDetermined` - Status: Passing
    * Tests that the HealthKitService handles authorization already determined
*   `testHealthKitUnavailable` - Status: Passing
    * Tests that the HealthKitService handles unavailable HealthKit
*   `testStartWorkoutSessionSuccessfully` - Status: Passing
    * Tests that the HealthKitService starts a workout session successfully
*   `testStartWorkoutSessionFailure` - Status: Passing
    * Tests that the HealthKitService handles workout session start failure
*   `testEndWorkoutSessionSuccessfully` - Status: Passing
    * Tests that the HealthKitService ends a workout session successfully
*   `testEndWorkoutSessionFailure` - Status: Passing
    * Tests that the HealthKitService handles workout session end failure
*   `testEndNonExistentWorkoutSession` - Status: Passing
    * Tests that the HealthKitService handles ending a non-existent workout session
*   `testEndWorkoutCollectionSuccess` - Status: Passing
    * Tests that the HealthKitService ends workout collection successfully
*   `testFinishWorkoutSuccess` - Status: Passing
    * Tests that the HealthKitService finishes workout successfully
*   `testCleanupWorkoutResources` - Status: Passing
    * Tests that the HealthKitService cleans up workout resources properly
*   `testCreateWorkoutConfiguration` - Status: Passing
    * Tests that the HealthKitService creates a workout configuration with the correct activity type and location type
*   `testEndExistingWorkoutSessionIfNeeded` - Status: Passing
    * Tests that the HealthKitService ends any existing workout session before starting a new one
*   `testSaveWorkoutDataWithEnergyAndHeartRate` - Status: Passing
    * Tests that the HealthKitService saves workout data to HealthKit with energy burned and heart rate
*   `testSaveWorkoutDataWithoutEnergyAndHeartRate` - Status: Passing
    * Tests that the HealthKitService saves workout data to HealthKit without energy burned or heart rate when permissions are denied
*   `testSaveWorkoutDataHandlesErrors` - Status: Passing
    * Tests that the HealthKitService handles errors when saving workout data
*   `testFinishWorkoutWithoutStartDate` - Status: Passing
    * Tests that the WorkoutCompleteViewModel handles finishing a workout without a start date

### Sync Functionality

*   `testSyncTriggeredOnRegainingConnection` - Status: Passing
    * Tests that sync is triggered when network connectivity is restored
*   `testSyncTriggeredOnAppLaunch` - Status: Passing
    * Tests that sync is triggered when the app is launched
*   `testSuccessfulSyncUpdatesLocalFlag` - Status: Passing
    * Tests that successful sync updates the local needsSync flag
*   `testSyncHandlesAPIErrorsGracefully` - Status: Passing
    * Tests that API errors are handled gracefully during sync
*   `testSyncSendsDataInOrder` - Status: Passing
    * Tests that data is sent in the correct order (oldest first)

### Error Handling

*   `testErrorBannerViewInitialization` - Status: Passing
    * Tests that the ErrorBannerView initializes correctly with the provided message and action
*   `testErrorBannerViewWithoutAction` - Status: Passing
    * Tests that the ErrorBannerView initializes correctly with just a message and no action
*   `testErrorBannerViewDismissAfterDelay` - Status: Passing
    * Tests that the ErrorBannerView auto-dismisses after the specified delay
*   `testShowError` - Status: Passing
    * Tests that the ErrorHandlingService shows an error message correctly
*   `testDismissError` - Status: Passing
    * Tests that the ErrorHandlingService dismisses an error message correctly
*   `testAutoDismissError` - Status: Passing
    * Tests that the ErrorHandlingService auto-dismisses an error message after the specified delay
*   `testHandleAPIError` - Status: Passing
    * Tests that the ErrorHandlingService handles API errors correctly
*   `testHandleNetworkError` - Status: Passing
    * Tests that the ErrorHandlingService handles network errors correctly
*   `testHandleGenericError` - Status: Passing
    * Tests that the ErrorHandlingService handles generic errors correctly