import SwiftUI

struct ContentView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Image(systemName: "applewatch")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                VStack(spacing: 16) {
                    Text("Dr. Muscle")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Companion App")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                
                VStack(spacing: 12) {
                    Text("Your Dr. Muscle watch app is ready!")
                        .font(.headline)
                        .multilineTextAlignment(.center)
                    
                    Text("Open the Dr. Muscle app on your Apple Watch to start tracking your workouts.")
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Dr. Muscle")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ContentView()
}
