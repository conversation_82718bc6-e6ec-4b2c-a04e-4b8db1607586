import XCTest
import AuthenticationServices
@testable import DrMuscleWatchApp

class AuthenticationManagerTests: XCTestCase {
    var sut: AuthenticationManager!
    
    override func setUp() {
        super.setUp()
        sut = AuthenticationManager.shared
        // Clear any existing auth state before each test
        sut.clearAuthState()
    }
    
    override func tearDown() {
        sut.clearAuthState()
        sut = nil
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testAuthenticationManagerInitialState() {
        // Given a fresh authentication manager
        // When checking initial state
        // Then user should not be authenticated
        XCTAssertFalse(sut.isAuthenticated)
        XCTAssertNil(sut.currentUser)
        XCTAssertNil(sut.authError)
    }
    
    // MARK: - Sign in with Apple Tests
    
    func testHandleSuccessfulSignInWithApple() async {
        // Given a successful Apple ID credential
        let credential = MockASAuthorizationAppleIDCredential(
            user: "test-user-id",
            email: "<EMAIL>",
            fullName: PersonNameComponents(givenName: "Test", familyName: "User")
        )
        
        // When handling the credential
        await sut.handleSignInWithApple(credential: credential)
        
        // Then user should be authenticated
        XCTAssertTrue(sut.isAuthenticated)
        XCTAssertNotNil(sut.currentUser)
        XCTAssertEqual(sut.currentUser?.email, "<EMAIL>")
        XCTAssertNil(sut.authError)
    }
    
    func testHandleSignInWithAppleError() async {
        // Given an error during sign in
        let error = NSError(domain: "com.drmuscle.test", code: -1, userInfo: [NSLocalizedDescriptionKey: "Sign in failed"])
        
        // When handling the error
        await sut.handleSignInWithAppleError(error)
        
        // Then user should not be authenticated and error should be set
        XCTAssertFalse(sut.isAuthenticated)
        XCTAssertNil(sut.currentUser)
        XCTAssertEqual(sut.authError, "Sign in failed")
    }
    
    // MARK: - Token Storage Tests
    
    func testStoreAndRetrieveAuthState() {
        // Given a user info model
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "test-token",
            firstName: "Test",
            lastName: "User"
        )
        
        // When storing auth state
        sut.storeAuthState(userInfo: userInfo)
        
        // Then auth state should be persisted
        XCTAssertTrue(sut.isAuthenticated)
        XCTAssertEqual(sut.currentUser?.id, "123")
        XCTAssertEqual(sut.currentUser?.token, "test-token")
        
        // And when creating a new instance
        let newAuthManager = AuthenticationManager()
        
        // Then it should load the persisted state
        XCTAssertTrue(newAuthManager.isAuthenticated)
        XCTAssertEqual(newAuthManager.currentUser?.id, "123")
    }
    
    func testClearAuthState() {
        // Given a stored auth state
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "test-token",
            firstName: "Test",
            lastName: "User"
        )
        sut.storeAuthState(userInfo: userInfo)
        
        // When clearing auth state
        sut.clearAuthState()
        
        // Then auth state should be cleared
        XCTAssertFalse(sut.isAuthenticated)
        XCTAssertNil(sut.currentUser)
        
        // And when creating a new instance
        let newAuthManager = AuthenticationManager()
        
        // Then it should not have any auth state
        XCTAssertFalse(newAuthManager.isAuthenticated)
        XCTAssertNil(newAuthManager.currentUser)
    }
    
    // MARK: - ASAuthorizationControllerDelegate Tests
    
    func testAuthorizationControllerDidCompleteWithAuthorization() {
        // Given a mock authorization
        let credential = MockASAuthorizationAppleIDCredential(
            user: "test-user",
            email: "<EMAIL>",
            fullName: nil
        )
        let authorization = MockASAuthorization(credential: credential)
        let controller = MockASAuthorizationController()
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithAuthorization: authorization)
        
        // Then it should handle the credential
        // Note: This test will need to wait for async processing
        let expectation = XCTestExpectation(description: "Auth processing")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            XCTAssertTrue(self.sut.isAuthenticated)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testAuthorizationControllerDidCompleteWithError() {
        // Given an error
        let error = NSError(domain: ASAuthorizationError.errorDomain, code: ASAuthorizationError.canceled.rawValue)
        let controller = MockASAuthorizationController()
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithError: error)
        
        // Then it should handle the error
        let expectation = XCTestExpectation(description: "Error processing")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            XCTAssertFalse(self.sut.isAuthenticated)
            XCTAssertNotNil(self.sut.authError)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
    }
}

// MARK: - Mock Classes

class MockASAuthorizationAppleIDCredential: ASAuthorizationAppleIDCredential {
    private let mockUser: String
    private let mockEmail: String?
    private let mockFullName: PersonNameComponents?
    
    init(user: String, email: String?, fullName: PersonNameComponents?) {
        self.mockUser = user
        self.mockEmail = email
        self.mockFullName = fullName
        super.init(coder: NSCoder())!
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var user: String { mockUser }
    override var email: String? { mockEmail }
    override var fullName: PersonNameComponents? { mockFullName }
}

class MockASAuthorization: ASAuthorization {
    private let mockCredential: ASAuthorizationCredential
    
    init(credential: ASAuthorizationCredential) {
        self.mockCredential = credential
        super.init()
    }
    
    override var credential: ASAuthorizationCredential { mockCredential }
}

class MockASAuthorizationController: ASAuthorizationController {
    override init(authorizationRequests: [ASAuthorizationRequest]) {
        super.init(authorizationRequests: authorizationRequests)
    }
} 