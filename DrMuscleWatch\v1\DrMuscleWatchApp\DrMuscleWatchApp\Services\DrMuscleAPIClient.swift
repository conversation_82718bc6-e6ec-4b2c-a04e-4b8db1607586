import Foundation

/// API client for communicating with the Dr. Muscle backend
class DrMuscleAPIClient {
    /// Shared instance
    static let shared = DrMuscleAPIClient()
    
    /// Base URL for the API
    private let baseURL = "https://dr-muscle-api.azurewebsites.net/"
    
    /// Authentication token
    private var token: String? {
        didSet {
            if let token = token {
                // Store token in keychain
                storeToken(token)
            } else {
                // Clear token from keychain
                clearToken()
            }
        }
    }
    
    /// Private initializer to enforce singleton
    private init() {
        // Load token from keychain if available
        self.token = loadToken()
    }
    
    // MARK: - Authentication
    
    /// Signs in with Apple ID token
    /// - Parameter idToken: The identity token from Apple
    /// - Returns: User information model
    func signInWithApple(idToken: String) async throws -> UserInfosModel {
        let requestModel = ["token": idToken]
        return try await postJSON(route: "api/Account/RegisterWithApple", model: requestModel)
    }
    
    // MARK: - Private Methods
    
    /// Generic POST request with JSON
    private func postJSON<T: Decodable, U: Encodable>(route: String, model: U?) async throws -> T {
        guard let url = URL(string: baseURL + route) else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authentication token if available
        if let token = token {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // Add body if provided
        if let model = model {
            request.httpBody = try JSONEncoder().encode(model)
        }
        
        // Perform the request
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // Check for HTTP errors
        if let httpResponse = response as? HTTPURLResponse {
            guard (200...299).contains(httpResponse.statusCode) else {
                throw APIError.httpError(statusCode: httpResponse.statusCode)
            }
        }
        
        // Decode the response
        do {
            let decodedResponse = try JSONDecoder().decode(T.self, from: data)
            return decodedResponse
        } catch {
            throw APIError.decodingError(error)
        }
    }
    
    // MARK: - Token Management
    
    /// Stores the authentication token in the keychain
    private func storeToken(_ token: String) {
        let tokenData = token.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAPIToken",
            kSecValueData as String: tokenData
        ]
        
        // First try to update the existing item
        var status = SecItemUpdate(query as CFDictionary, [kSecValueData as String: tokenData] as CFDictionary)
        
        // If the item doesn't exist, add it
        if status == errSecItemNotFound {
            status = SecItemAdd(query as CFDictionary, nil)
        }
    }
    
    /// Loads the authentication token from the keychain
    private func loadToken() -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAPIToken",
            kSecReturnData as String: true
        ]
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status == errSecSuccess,
              let tokenData = item as? Data,
              let token = String(data: tokenData, encoding: .utf8) else {
            return nil
        }
        
        return token
    }
    
    /// Clears the authentication token from the keychain
    private func clearToken() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "DrMuscleAPIToken"
        ]
        
        SecItemDelete(query as CFDictionary)
    }
    
    /// Sets the authentication token (usually after successful login)
    func setAuthToken(_ token: String) {
        self.token = token
    }
    
    /// Clears the authentication token (usually after logout)
    func clearAuthToken() {
        self.token = nil
    }
}

// MARK: - API Error

enum APIError: LocalizedError {
    case invalidURL
    case httpError(statusCode: Int)
    case decodingError(Error)
    case noData
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .httpError(let statusCode):
            return "HTTP Error: \(statusCode)"
        case .decodingError(let error):
            return "Decoding Error: \(error.localizedDescription)"
        case .noData:
            return "No data received"
        }
    }
}