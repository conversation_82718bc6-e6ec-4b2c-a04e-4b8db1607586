# Dr. Muscle Apple Watch App - Development Instructions

## Project Overview

You are working on the Dr. Muscle Apple Watch app, which is an **independent companion watchOS app**. This is a specific type of Apple Watch app that combines the benefits of both standalone functionality and iOS integration.

## Critical Context - App Architecture Evolution

### IMPORTANT: This is an Independent Companion App (NOT Watch-Only)

The app architecture has evolved during development:
- **Originally planned**: Watch-only app (no iOS component at all)
- **Current implementation**: Independent companion app (has both iOS and watchOS components)
- **Why this matters**: The build process, App Store submission, and user experience are completely different

### What is an Independent Companion App?

An independent companion app means:
1. **Two apps in one bundle**: Contains both an iOS app and a watchOS app
2. **Independent operation**: The watchOS app can run WITHOUT the iOS app installed
3. **Single App Store listing**: Users see one app that works on both platforms
4. **Flexible installation**: Users can install:
   - Just the iOS app
   - Just the watchOS app
   - Both apps together
5. **Shared purchases**: In-app purchases and subscriptions work across both platforms

### This is NOT a Watch-Only App

A watch-only app would:
- Have NO iOS component at all
- Use `INFOPLIST_KEY_WKWatchOnly = YES` (we do NOT use this)
- Be distributed differently on the App Store
- Have limited functionality compared to companion apps

### Bundle Identifiers
- **iOS Companion App**: `com.drmaxmuscle.max`
- **watchOS App**: `com.drmaxmuscle.max.watchkitapp`

## Development Workflow

### Memory Bank System
The project follows a strict "Memory Bank" system documented in `DrMuscleWatch/docs/rules.md`. Key points:

1. **Always start by reading** these files in order:
   - `DrMuscleWatch/docs/rules.md` - Technical specifications and workflow
   - `DrMuscleWatch/docs/architecture.md` - System architecture and patterns
   - `DrMuscleWatch/docs/plan.md` - High-level project plan
   - `DrMuscleWatch/docs/todo.md` - Current tasks and requirements
   - `DrMuscleWatch/docs/status.md` - Current progress and known issues
   - `DrMuscleWatch/docs/testing.md` - Test implementation status

2. **Follow TDD (Test-Driven Development)**:
   - Write failing tests first (RED stage)
   - Implement minimal code to pass tests (GREEN stage)
   - Refactor and improve (REFACTOR stage)
   - Update documentation (DOCUMENTATION stage)

3. **Use BDD-style XCTest** with Given/When/Then structure
4. **Make incremental changes** - one small, logical change at a time
5. **Verify everything** - test after every change

### Project Structure
```
DrMuscleWatch/v1/DrMuscleWatchApp/
├── DrMuscleWatchApp/          # watchOS app source
│   ├── Models/                # Core Data models
│   ├── Services/              # API, storage, sync services
│   ├── Views/                 # SwiftUI views
│   └── ViewModels/           # MVVM view models
├── iOS/                       # iOS companion app
└── DrMuscleWatchApp.xcodeproj/
```

## Build Configuration

### Xcode Settings
The project requires specific configuration for independent companion apps:

**watchOS Target** (`DrMuscleWatchApp`):
- `INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES` ← Makes it independent
- `INFOPLIST_KEY_LSApplicationLaunchProhibited = YES` ← Required for watchOS
- `SKIP_INSTALL = YES` ← Critical: Embeds watch app inside iOS app
- `INFOPLIST_KEY_CFBundleIconName = AppIcon`
- **Never set**: `INFOPLIST_KEY_WKWatchOnly` (this would make it watch-only)

**iOS Target** (`DrMuscleWatchApp-iOS`):
- `SKIP_INSTALL = NO` ← Critical: Makes iOS app the top-level product
- `INFOPLIST_KEY_CFBundleIconName = AppIcon`
- Contains minimal UI directing users to install on Apple Watch

### GitHub Actions / Build Process

**CRITICAL**: Even though the watch app can run independently, App Store submission requires:
- **Scheme**: `DrMuscleWatchApp-iOS` (the iOS scheme, NOT the watch scheme!)
- **Archive Destination**: `generic/platform=iOS` (NOT watchOS!)
- **Upload Type**: `-t ios` (NOT watchos!)

This is because:
1. Apple requires independent companion apps to be submitted as iOS apps
2. The watchOS app is embedded inside the iOS app archive
3. The App Store extracts and handles both apps from the single submission

## API Integration

When implementing API features, refer to `DrMuscleWatch/docs/api-reference.md`. Key endpoints:

- **Authentication**: `/api/Account/RegisterWithApple`
- **Workout List**: `/api/Workout/GetUserWorkoutTemplateGroup`
- **Exercise Details**: `/api/Exercise/GetRecommendationForExercise`
- **Log Set**: `/api/Exercise/AddWorkoutLogSerieNew`
- **Complete Workout**: `/api/Workout/SaveWorkoutV3Pro`

### Required API Flags
The API must provide these flags for each set:
- `isWarmup` - Identifies warm-up sets
- `isFirstWorkSet` - First work set (prompts for RIR)
- `isLastPlannedSet` - Last planned set (shows "Add Set" option)
- `restDurationSeconds` - Rest period after set

## Testing Guidelines

1. **Run tests frequently**: After every code change
2. **Aim for high coverage**: 100% for core logic, >90% overall
3. **Use real implementations**: Avoid mocks except for system boundaries
4. **Test file naming**: `*Tests.swift` in the test target
5. **Update `DrMuscleWatch/docs/testing.md`**: Track all test status

## Common Tasks

### Adding a New Feature
1. Read the Memory Bank files (especially `DrMuscleWatch/docs/todo.md`)
2. Write failing tests for the feature
3. Implement the minimal code to pass tests
4. Refactor and improve
5. Update documentation files

### Running Tests
```bash
# Run all tests
xcodebuild test -scheme DrMuscleWatchApp -destination 'platform=watchOS Simulator,name=Apple Watch Series 9 (45mm)'

# Run specific test
xcodebuild test -scheme DrMuscleWatchApp -only-testing:DrMuscleWatchAppTests/SetViewModelTests
```

### Debugging Build Issues
1. Check `DrMuscleWatch/docs/status.md` for known issues and solutions
2. Verify bundle identifiers match configuration
3. Ensure all required Info.plist keys are set
4. Check asset catalogs have all required icon sizes

## Remote Development

If using the remote Mac mini setup (see `DrMuscleWatch/docs/remote-development-setup.md`):

1. **SSH to Mac**: `ssh m1@51.159.121.70`
2. **Pull changes**: `cd ~/DrMuscle && git pull origin Development_Watch_Carl_v1.1`
3. **Open Xcode**: `open -a Xcode ~/DrMuscle/DrMuscleWatch/v1/DrMuscleWatchApp/DrMuscleWatchApp.xcodeproj`
4. **Use VNC**: Connect to `51.159.121.70:59010` for visual access

## Key Architecture Decisions

Documented in `DrMuscleWatch/docs/adr/` (Architecture Decision Records):

1. **Standalone Architecture**: Independent watch app with offline capability
2. **Swift/SwiftUI Stack**: Native technologies for best performance
3. **Sign in with Apple**: Secure authentication without passwords
4. **HealthKit Integration**: Track workouts and health metrics
5. **Tap-to-Open Pickers**: Better UX for small screen
6. **RIR After First Work Set**: Capture effort level at optimal time

## Current Development Status

As of the last update in `DrMuscleWatch/docs/status.md`:
- Core project structure is complete
- Authentication and basic UI implemented
- Build configuration fixed for App Store submission
- Ready for feature development

## Important Notes

1. **This is NOT a watch-only app** - It's an independent companion app with both iOS and watchOS components
2. **Never use `INFOPLIST_KEY_WKWatchOnly`** - This would convert it to watch-only (breaking everything!)
3. **Always archive the iOS scheme** (`DrMuscleWatchApp-iOS`) for App Store submission, never the watch scheme
4. **The iOS app exists solely as a container** - It has minimal UI just to direct users to the watch
5. **Test on real devices** when possible for accurate performance
6. **Follow Apple's HIG** for watchOS design patterns
7. **Keep methods small** - watch apps have memory constraints

## Troubleshooting

### Build Failures
- Check asset catalog naming matches Contents.json
- Verify all icon sizes are present and correct dimensions
- Ensure DEVELOPMENT_ASSET_PATHS exists

### TestFlight Upload Errors
- Archive must be from iOS scheme, not watch scheme
- Verify bundle identifiers and Info.plist keys
- Check provisioning profiles match bundle IDs

### API Integration Issues
- Ensure authentication token is stored in Keychain
- Handle offline scenarios with local storage
- Implement retry logic for failed requests

## Next Steps

Refer to `DrMuscleWatch/docs/todo.md` for the current task list and `DrMuscleWatch/docs/plan.md` for the overall project roadmap. The project follows a strict TDD approach with comprehensive documentation updates after each task.

Remember: Always read the Memory Bank files before starting work, and update them after completing tasks!