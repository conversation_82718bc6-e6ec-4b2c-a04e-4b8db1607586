import XCTest
import AuthenticationServices
@testable import DrMuscleWatchApp

class LoginViewModelTests: XCTestCase {
    var sut: LoginViewModel!
    
    override func setUp() {
        super.setUp()
        sut = LoginViewModel()
        // Clear any existing auth state
        AuthenticationManager.shared.clearAuthState()
    }
    
    override func tearDown() {
        sut = nil
        AuthenticationManager.shared.clearAuthState()
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testLoginViewModelInitialState() {
        // Given a new login view model
        // When checking initial state
        // Then all properties should have default values
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    // MARK: - Sign in with Apple Tests
    
    func testStartSignInWithApple() {
        // Given the login view model
        // When starting Sign in with Apple
        sut.startSignInWithApple()
        
        // Then it should set authenticating state
        XCTAssertTrue(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testHandleSuccessfulSignIn() async {
        // Given a successful sign in
        let credential = MockASAuthorizationAppleIDCredential(
            user: "test-user",
            email: "<EMAIL>",
            fullName: PersonNameComponents(givenName: "Test", familyName: "User")
        )
        
        // When handling the credential
        await sut.handleSignInWithAppleSuccess(credential: credential)
        
        // Then authentication should complete successfully
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testHandleSignInCancellation() {
        // Given a sign in cancellation
        let error = NSError(domain: ASAuthorizationError.errorDomain, code: ASAuthorizationError.canceled.rawValue)
        
        // When handling the cancellation
        sut.handleSignInWithAppleError(error)
        
        // Then it should reset state without showing error
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage) // Cancellation shouldn't show error
    }
    
    func testHandleSignInError() {
        // Given a sign in error
        let error = NSError(domain: ASAuthorizationError.errorDomain, code: ASAuthorizationError.failed.rawValue)
        
        // When handling the error
        sut.handleSignInWithAppleError(error)
        
        // Then it should show error message
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertEqual(sut.errorMessage, "Sign in failed. Please try again.")
    }
    
    func testHandleNetworkError() {
        // Given a network error
        let error = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet)
        
        // When handling the error
        sut.handleSignInWithAppleError(error)
        
        // Then it should show network error message
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertEqual(sut.errorMessage, "Network connection required. Please check your connection.")
    }
    
    // MARK: - Backend Integration Tests
    
    func testHandleBackendValidationSuccess() async {
        // Given a successful backend validation
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "valid-token",
            firstName: "Test",
            lastName: "User"
        )
        
        // When backend validation succeeds
        await sut.handleBackendValidationSuccess(userInfo: userInfo)
        
        // Then authentication should complete
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testHandleBackendValidationError() async {
        // Given a backend validation error
        let error = NSError(domain: "com.drmuscle.api", code: 401, userInfo: [NSLocalizedDescriptionKey: "Invalid credentials"])
        
        // When backend validation fails
        await sut.handleBackendValidationError(error)
        
        // Then it should show appropriate error
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertEqual(sut.errorMessage, "Could not connect to Dr. Muscle. Please try again.")
    }
    
    // MARK: - Helper Methods Tests
    
    func testCreateAuthorizationRequest() {
        // Given the view model
        // When creating an authorization request
        let request = sut.createAuthorizationRequest()
        
        // Then it should be properly configured
        XCTAssertEqual(request.requestedScopes, [.fullName, .email])
    }
    
    func testShouldRemoveQuickSignInMethod() {
        // Given the login view model
        // When checking for quickSignIn method
        // Then it should not exist (as we're removing mock login)
        let mirror = Mirror(reflecting: sut)
        let hasQuickSignIn = mirror.children.contains { $0.label == "quickSignIn" }
        XCTAssertFalse(hasQuickSignIn)
    }
}

// MARK: - Mock ASAuthorizationAppleIDCredential

private class MockASAuthorizationAppleIDCredential: ASAuthorizationAppleIDCredential {
    private let mockUser: String
    private let mockEmail: String?
    private let mockFullName: PersonNameComponents?
    private let mockIdentityToken: Data?
    
    init(user: String, email: String?, fullName: PersonNameComponents?, identityToken: Data? = nil) {
        self.mockUser = user
        self.mockEmail = email
        self.mockFullName = fullName
        self.mockIdentityToken = identityToken ?? "mock-token".data(using: .utf8)
        super.init(coder: NSCoder())!
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var user: String { mockUser }
    override var email: String? { mockEmail }
    override var fullName: PersonNameComponents? { mockFullName }
    override var identityToken: Data? { mockIdentityToken }
} 