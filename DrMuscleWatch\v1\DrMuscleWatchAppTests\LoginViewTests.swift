import XCTest
import SwiftUI
import ViewInspector
import AuthenticationServices
@testable import DrMuscleWatchApp

class LoginViewTests: XCTestCase {
    
    // MARK: - Sign in with Apple Button Tests
    
    func testLoginViewDisplaysSignInWithAppleButton() throws {
        // Given the login view
        let sut = LoginView()
            .environmentObject(AuthenticationManager.shared)
        
        // When the view is rendered
        // Then it should display the Sign in with Apple button
        let signInButton = try sut.inspect().scrollView().vStack().find(SignInWithAppleButton.self)
        XCTAssertNotNil(signInButton)
    }
    
    func testSignInWithAppleButtonHasCorrectStyle() throws {
        // Given the login view
        let sut = LoginView()
            .environmentObject(AuthenticationManager.shared)
        
        // When inspecting the Sign in with Apple button
        let signInButton = try sut.inspect().scrollView().vStack().find(SignInWithAppleButton.self)
        
        // Then it should have the correct style for watchOS
        // Note: We'll verify the style matches Apple's guidelines
        XCTAssertNotNil(signInButton)
    }
    
    func testTappingSignInWithAppleButtonInitiatesSignIn() throws {
        // Given the login view with a view model
        let viewModel = LoginViewModel()
        let sut = LoginView(viewModel: viewModel)
            .environmentObject(AuthenticationManager.shared)
        
        // When the Sign in with Apple button is tapped
        let signInButton = try sut.inspect().scrollView().vStack().find(SignInWithAppleButton.self)
        
        // Then it should trigger the sign in process
        // Note: We'll verify that the authorization controller is presented
        XCTAssertNotNil(signInButton)
    }
    
    func testLoginViewDoesNotShowQuickSignInButton() throws {
        // Given the login view
        let sut = LoginView()
            .environmentObject(AuthenticationManager.shared)
        
        // When searching for the quick sign in button
        // Then it should not be found (as we're removing the mock login)
        XCTAssertThrowsError(try sut.inspect().scrollView().vStack().find(button: "Quick Sign In"))
    }
    
    func testLoginViewShowsAppBranding() throws {
        // Given the login view
        let sut = LoginView()
            .environmentObject(AuthenticationManager.shared)
        
        // When the view is rendered
        // Then it should show the app logo and name
        let logo = try sut.inspect().scrollView().vStack().find(ViewType.Image.self)
        XCTAssertNotNil(logo)
        
        let appName = try sut.inspect().scrollView().vStack().find(text: "Dr. Muscle")
        XCTAssertNotNil(appName)
    }
    
    func testLoginViewShowsErrorMessage() throws {
        // Given a login view with an error
        let viewModel = LoginViewModel()
        viewModel.errorMessage = "Authentication failed"
        let sut = LoginView(viewModel: viewModel)
            .environmentObject(AuthenticationManager.shared)
        
        // When the view is rendered
        // Then it should display the error message
        let errorText = try sut.inspect().scrollView().vStack().find(text: "Authentication failed")
        XCTAssertNotNil(errorText)
    }
    
    func testLoginViewShowsLoadingIndicatorDuringAuth() throws {
        // Given a login view that is authenticating
        let viewModel = LoginViewModel()
        viewModel.isAuthenticating = true
        let sut = LoginView(viewModel: viewModel)
            .environmentObject(AuthenticationManager.shared)
        
        // When the view is rendered
        // Then it should show a loading indicator
        let progressView = try sut.inspect().scrollView().vStack().find(ViewType.ProgressView.self)
        XCTAssertNotNil(progressView)
    }
    
    func testSignInButtonDisabledDuringAuthentication() throws {
        // Given a login view that is authenticating
        let viewModel = LoginViewModel()
        viewModel.isAuthenticating = true
        let sut = LoginView(viewModel: viewModel)
            .environmentObject(AuthenticationManager.shared)
        
        // When inspecting the Sign in with Apple button
        let signInButton = try sut.inspect().scrollView().vStack().find(SignInWithAppleButton.self)
        
        // Then it should be disabled
        let isDisabled = try signInButton.callOnTapGesture()
        // Note: We'll need to verify the button is actually disabled during authentication
        XCTAssertNotNil(signInButton)
    }
}

// MARK: - Custom SignInWithAppleButton for Testing

struct SignInWithAppleButton: View {
    let onRequest: (ASAuthorizationAppleIDRequest) -> Void
    let onCompletion: (Result<ASAuthorization, Error>) -> Void
    
    var body: some View {
        Button(action: {
            // Trigger sign in
        }) {
            HStack {
                Image(systemName: "applelogo")
                Text("Sign in with Apple")
            }
        }
        .buttonStyle(.borderedProminent)
    }
} 