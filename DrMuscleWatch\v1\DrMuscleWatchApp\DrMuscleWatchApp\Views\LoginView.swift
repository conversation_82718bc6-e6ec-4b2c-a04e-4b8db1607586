import SwiftUI

/// View for the login screen on watchOS
struct LoginView: View {
    /// The view model for the login screen
    @StateObject private var viewModel = LoginViewModel()

    /// The authentication manager
    @EnvironmentObject private var authManager: AuthenticationManager

    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // App logo
                Image(systemName: "dumbbell.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(.yellow)

                // App name
                Text("Dr. Muscle")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                // Welcome message
                Text("Watch App")
                    .font(.caption)
                    .foregroundColor(.gray)

                // Quick sign in button (for testing)
                Button("Quick Sign In") {
                    viewModel.quickSignIn()
                }
                .buttonStyle(.borderedProminent)
                .tint(.yellow)
                .disabled(viewModel.isAuthenticating)

                // Error message
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .font(.caption2)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                }

                // Loading indicator
                if viewModel.isAuthenticating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .yellow))
                        .scaleEffect(0.8)
                }
            }
            .padding()
        }
    }
}

#Preview {
    LoginView()
        .environmentObject(AuthenticationManager.shared)
}
