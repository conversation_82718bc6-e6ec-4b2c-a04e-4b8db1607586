import Foundation
import SwiftUI
import AuthenticationServices

/// View model for the login screen on watchOS
class LoginViewModel: ObservableObject {
    /// The authentication manager
    private let authManager = AuthenticationManager.shared
    
    /// Whether authentication is in progress
    @Published var isAuthenticating: Bool = false
    
    /// Error message to display
    @Published var errorMessage: String?
    
    /// The current authorization controller
    private var currentAuthorizationController: ASAuthorizationController?
    
    
    /// Starts the Sign in with Apple flow
    func startSignInWithApple() {
        isAuthenticating = true
        errorMessage = nil
        
        let request = createAuthorizationRequest()
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = authManager
        
        // Store reference to prevent deallocation
        currentAuthorizationController = authorizationController
        
        // Perform the authorization
        authorizationController.performRequests()
        
        // Monitor auth manager for changes
        Task {
            // Wait a bit for the authorization to complete
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            
            while isAuthenticating {
                await MainActor.run {
                    if authManager.isAuthenticated {
                        self.isAuthenticating = false
                        self.errorMessage = nil
                    } else if let error = authManager.authError {
                        self.isAuthenticating = false
                        self.errorMessage = error
                    }
                }
                
                if isAuthenticating {
                    try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                }
            }
            
            // Clear the reference
            await MainActor.run {
                self.currentAuthorizationController = nil
            }
        }
    }
    
    /// Creates an authorization request for Sign in with Apple
    private func createAuthorizationRequest() -> ASAuthorizationAppleIDRequest {
        let provider = ASAuthorizationAppleIDProvider()
        let request = provider.createRequest()
        request.requestedScopes = [.fullName, .email]
        return request
    }
}
