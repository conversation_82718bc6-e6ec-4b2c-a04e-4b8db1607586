import Foundation
import SwiftUI

/// View model for the login screen on watchOS
/// Uses Sign in with Apple exclusively per ADR-0003
class LoginViewModel: ObservableObject {
    /// The authentication manager
    private let authManager = AuthenticationManager.shared

    /// Whether authentication is in progress
    @Published var isAuthenticating: Bool = false

    /// Error message to display
    @Published var errorMessage: String?

    /// Signs in with Apple (only authentication method for watch)
    func signInWithApple() {
        isAuthenticating = true
        errorMessage = nil

        // Trigger Sign in with Apple flow
        authManager.signInWithApple()

        // Monitor authentication state changes
        Task {
            // Wait a moment for the auth flow to complete
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            await MainActor.run {
                isAuthenticating = false
                if let error = authManager.authError {
                    errorMessage = error
                }
            }
        }
    }
}
