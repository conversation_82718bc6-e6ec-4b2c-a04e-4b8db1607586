import Foundation
import SwiftUI

/// View model for the login screen on watchOS
class LoginViewModel: ObservableObject {
    /// The authentication manager
    private let authManager = AuthenticationManager.shared
    
    /// Email input field
    @Published var email: String = ""
    
    /// Password input field
    @Published var password: String = ""
    
    /// Whether authentication is in progress
    @Published var isAuthenticating: Bool = false
    
    /// Error message to display
    @Published var errorMessage: String?
    
    /// Signs in with email and password
    func signIn() {
        guard !email.isEmpty && !password.isEmpty else {
            errorMessage = "Please enter both email and password"
            return
        }
        
        isAuthenticating = true
        errorMessage = nil
        
        Task {
            await authManager.signIn(email: email, password: password)
            
            await MainActor.run {
                isAuthenticating = false
                if let error = authManager.authError {
                    errorMessage = error
                }
            }
        }
    }
    
    /// Quick sign in for testing (removes need for typing on watch)
    func quickSignIn() {
        email = "<EMAIL>"
        password = "password"
        signIn()
    }
}
