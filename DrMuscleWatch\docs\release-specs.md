# Dr. Muscle Apple Watch App - Project Specifications

## Project Overview

### Main Goal
Build and distribute an **independent companion watchOS app** for Dr. Muscle that can be installed and run without requiring the iOS companion app to be installed, while still being distributed together as a single App Store purchase.

### App Type: Independent Companion watchOS App
Based on Apple's official documentation, this project creates an **independent companion watchOS app**, which means:
- The watchOS app can be installed and run without the iOS app being installed
- Both apps are distributed together as a single App Store purchase
- Users can choose to install the watchOS app, iOS app, or both
- The watchOS app appears as an independent app on Apple Watch
- Universal purchases and in-app purchases work across both platforms

This is **NOT** a watch-only app (which would have no iOS component) but rather an independent companion app that provides the best user experience.

## Technical Architecture

### Project Structure
```
DrMuscleWatch/v1/DrMuscleWatchApp/
├── DrMuscleWatchApp/                    # watchOS app source code
│   ├── Assets.xcassets/                 # watchOS app icons and assets
│   ├── Models/                          # Core Data models and user data
│   ├── Services/                        # Authentication, storage, sync services
│   ├── Views/                           # SwiftUI views for watchOS
│   └── ViewModels/                      # MVVM architecture
├── iOS/                                 # iOS companion app source code
│   ├── Assets.xcassets/                 # iOS app icons and assets
│   ├── App.swift                        # iOS app entry point
│   └── ContentView.swift                # iOS app main view
└── DrMuscleWatchApp.xcodeproj/          # Xcode project configuration
```

### Bundle Identifiers
- **iOS Companion App**: `com.drmaxmuscle.max`
- **watchOS App**: `com.drmaxmuscle.max.watchkitapp`

### Key Configuration Settings

#### watchOS Target (`DrMuscleWatchApp`)
```
INFOPLIST_KEY_WKRunsIndependentlyOfCompanionApp = YES  # Enables independence
INFOPLIST_KEY_LSApplicationLaunchProhibited = YES      # Required for watchOS apps
INFOPLIST_KEY_CFBundleSupportedPlatforms = WatchOS     # Platform specification
INFOPLIST_KEY_UIDeviceFamily = 4                       # Apple Watch device family
INFOPLIST_KEY_CFBundleIconName = AppIcon               # Icon asset reference
INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.drmaxmuscle.max  # Links to iOS app
SKIP_INSTALL = YES                                     # Prevents top-level installation
```

#### iOS Target (`DrMuscleWatchApp-iOS`)
```
INFOPLIST_KEY_CFBundleIconName = AppIcon               # Icon asset reference
SKIP_INSTALL = NO                                      # Allows top-level installation
TARGETED_DEVICE_FAMILY = "1,2"                        # iPhone and iPad support
```

### Build and Distribution Workflow

#### GitHub Actions Configuration
- **Scheme**: `DrMuscleWatchApp-iOS` (iOS scheme that embeds watchOS app)
- **Archive Destination**: `generic/platform=iOS` (not watchOS)
- **Upload Type**: `-t ios` for TestFlight upload
- **Export**: Creates IPA containing iOS app with embedded watchOS app in `/Watch` directory

#### Asset Catalog Requirements
Both iOS and watchOS targets require complete AppIcon asset sets:

**iOS AppIcon Requirements**:
- 120x120px for iPhone/iPod Touch (iOS >= 10.0)
- 152x152px for iPad (iOS >= 10.0)
- Complete set from 20x20 to 1024x1024 with proper platform specification

**watchOS AppIcon Requirements**:
- Complete set for all Apple Watch sizes (38mm, 40mm, 41mm, 44mm, 45mm, 49mm)
- Notification Center icons (24x24, 27.5x27.5)
- App Launcher icons (40x40 to 54x54)
- Quick Look icons (86x86 to 129x129)
- Marketing icon (1024x1024)

## Current Implementation Status

### ✅ Completed Features
1. **Project Structure**: Complete Xcode project with iOS and watchOS targets
2. **Core Data Stack**: Persistent storage for workouts, exercises, and set logs
3. **Authentication**: Sign in with Apple integration for watchOS
4. **Basic UI**: SwiftUI-based login and content views
5. **Independent App Configuration**: Proper settings for standalone operation
6. **Asset Catalogs**: Complete icon sets for both platforms
7. **Build Configuration**: GitHub Actions workflow for CI/CD

### 🔧 Recently Fixed Issues (June 2025)
- **AppIcon Naming Inconsistencies**: Fixed file naming to match Contents.json references
- **iOS Target Assets Missing**: Added iOS Assets.xcassets to iOS target resources
- **Path Configuration Error**: Corrected iOS Assets.xcassets path reference
- **Build Settings**: Proper SKIP_INSTALL and independence configuration
- **Export Configuration**: Fixed export options for App Store distribution

### 🚧 Current Development Phase
The project is in the **build and deployment optimization phase**. Core functionality is implemented, and the focus is on resolving build, archive, and TestFlight upload issues.

## Feature Roadmap

### Phase 1: Core Functionality (Implemented)
- [x] Sign in with Apple authentication
- [x] Core Data persistence layer
- [x] Basic workout data models
- [x] Independent app configuration

### Phase 2: Workout Management (In Progress)
- [ ] Workout selection and display
- [ ] Exercise progression tracking
- [ ] Set logging with reps, weight, and RIR
- [ ] Rest timer functionality
- [ ] Performance feedback calculations

### Phase 3: Sync and Connectivity
- [ ] Background sync service for API integration
- [ ] Offline-first architecture with local storage
- [ ] CloudKit or server-based data synchronization
- [ ] Push notifications for workout reminders

### Phase 4: Advanced Features
- [ ] HealthKit integration for workout sessions
- [ ] Haptic feedback for user interactions
- [ ] Complications for watch face integration
- [ ] Advanced analytics and progress tracking

## Development Guidelines

### Architecture Patterns
- **MVVM**: Model-View-ViewModel architecture with SwiftUI
- **Dependency Injection**: Services injected through environment
- **Offline-First**: Local storage with background sync
- **Protocol-Oriented**: Testable service interfaces

### Testing Strategy
- **Unit Tests**: Service layer and business logic
- **Integration Tests**: Core Data and API interactions
- **UI Tests**: Critical user flows on watchOS
- **Device Testing**: Real Apple Watch testing for performance

### Code Organization
```swift
// Service Layer
protocol AuthenticationService { }
protocol StorageService { }
protocol SyncService { }

// ViewModels
class LoginViewModel: ObservableObject { }
class WorkoutViewModel: ObservableObject { }

// Views
struct LoginView: View { }
struct WorkoutListView: View { }
```

## Build and Deployment

### Local Development
1. Open `DrMuscleWatchApp.xcodeproj` in Xcode
2. Select `DrMuscleWatchApp-iOS` scheme
3. Build for iOS Simulator or Device
4. watchOS app automatically builds and embeds

### CI/CD Pipeline
The project uses GitHub Actions for automated build and deployment:

1. **Trigger**: Push to `Development_Watch_Carl_v1.1` branch
2. **Build**: Xcode archive using iOS scheme
3. **Sign**: Manual code signing with distribution certificates
4. **Export**: IPA generation with proper export options
5. **Upload**: TestFlight upload using App Store Connect API
6. **Validation**: Automatic icon and Info.plist validation

### Required Secrets
```
APPSTORE_API_KEY_ID              # App Store Connect API key
APPSTORE_ISSUER_ID               # API issuer identifier
APPSTORE_API_PRIVATE_KEY         # Base64 encoded private key
P12_CERTIFICATE                  # Base64 encoded distribution certificate
P12_CERTIFICATE_PASSWORD         # Certificate password
IOS_PROVISIONING_PROFILE_BASE64  # iOS provisioning profile
WATCH_APP_PROVISIONING_PROFILE_BASE64  # watchOS provisioning profile
APPLE_TEAM_ID                    # Apple Developer Team ID (7AAXZ47995)
```

## Known Issues and Solutions

### Resolved Issues
1. **Export Method Error**: Fixed by removing `WKWatchOnly` setting and using iOS scheme
2. **Missing Icons**: Fixed AppIcon naming inconsistencies and added complete asset sets
3. **Info.plist Keys**: Added required CFBundleIconName and WKCompanionAppBundleIdentifier
4. **Asset Path Errors**: Corrected iOS Assets.xcassets path reference in project file

### Monitoring Points
- **Build Performance**: Archive time and size optimization
- **TestFlight Validation**: Icon and metadata compliance
- **Device Testing**: Performance on different Apple Watch models
- **Memory Usage**: Core Data and UI memory management

## Next Steps

### Immediate Priorities (Next 1-2 Weeks)
1. **Verify Build Success**: Confirm next GitHub Actions run completes successfully
2. **TestFlight Testing**: Deploy to internal testers and verify functionality
3. **Core Workout Flow**: Implement workout selection and basic exercise tracking
4. **API Integration**: Connect to Dr. Muscle backend for workout data

### Short-term Goals (Next Month)
1. **Complete Workout Management**: Full exercise progression and set logging
2. **Sync Implementation**: Background data synchronization
3. **HealthKit Integration**: Workout session tracking
4. **User Testing**: Beta testing with real users

### Long-term Vision (3-6 Months)
1. **App Store Release**: Public availability of independent watchOS app
2. **Advanced Features**: Complications, analytics, and performance insights
3. **Cross-Platform Sync**: Seamless data sync between iOS and watchOS
4. **User Onboarding**: Guided setup and tutorial flow

## Documentation References

### Apple Documentation
- [Creating independent watchOS apps](https://developer.apple.com/documentation/watchkit/creating-independent-watchos-apps)
- [App Store Connect API](https://developer.apple.com/documentation/appstoreconnectapi)
- [watchOS App Architecture](https://developer.apple.com/documentation/watchkit)

### Project Documentation
- `docs/status.md`: Detailed chronological progress and fixes
- `docs/apple-documentation.md`: Apple's official guidance on independent apps
- `docs/architecture.md`: Technical architecture decisions
- `docs/api-reference.md`: API integration specifications

## Team Handoff Information

### Development Environment Setup
1. **Xcode**: Version 15.0+ required for watchOS 10.0+ support
2. **Apple Developer Account**: Access to Team ID 7AAXZ47995
3. **Certificates**: Distribution certificate for code signing
4. **Provisioning Profiles**: iOS and watchOS profiles for bundle IDs

### Key Contacts and Resources
- **Bundle IDs**: Configured in Apple Developer Portal
- **App Store Connect**: App record for `com.drmaxmuscle.max`
- **GitHub Repository**: `dr-muscle/DrMuscle` on `Development_Watch_Carl_v1.1` branch
- **CI/CD**: GitHub Actions workflow in `.github/workflows/apple-watch-build-workflow.yml`

### Critical Success Factors
1. **Independent Operation**: watchOS app must function without iOS app
2. **App Store Compliance**: Proper asset catalogs and Info.plist configuration
3. **Build Automation**: Reliable CI/CD pipeline for continuous deployment
4. **User Experience**: Intuitive workout tracking on small screen form factor

This project represents a significant step forward in making Dr. Muscle accessible directly on Apple Watch, providing users with a seamless fitness tracking experience that works independently while maintaining integration with the broader Dr. Muscle ecosystem. 